package com.ruoyi.ai.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ruoyi.ai.domain.AiEmail;
import com.ruoyi.ai.domain.AiEmailAttachments;
import com.ruoyi.ai.domain.AiEmailConfig;
import com.ruoyi.ai.domain.AiEmailData;
import com.ruoyi.ai.service.IAiEmailAttachmentsService;
import com.ruoyi.ai.service.IAiEmailConfigService;
import com.ruoyi.ai.service.IAiEmailDataService;
import com.ruoyi.ai.service.IAiEmailService;
import com.ruoyi.ai.utils.mail.MailUtil;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;
import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.imap.IMAPStore;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.mail.*;
import javax.mail.event.MessageCountAdapter;
import javax.mail.event.MessageCountEvent;
import javax.mail.internet.MimeMessage;
import javax.mail.search.FlagTerm;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MultiEmailMonitor implements Runnable{

    // 依赖注入
    @Autowired private ISysUserService sysUserService;
    @Autowired private IAiEmailConfigService aiEmailConfigService;
    @Autowired private IAiEmailDataService aiEmailDataService;
    @Autowired private IAiEmailService aiEmailService;
    @Autowired private EmailScheduler emailScheduler;
    @Autowired private IAiEmailAttachmentsService aiEmailAttachmentsService;

    // 线程管理
    private ExecutorService executor;
    private final Map<Long, Future<?>> taskMap = new ConcurrentHashMap<>();
    private final BiConsumer<Long, AiEmailData> emailProcessor = this::processEmailData;

    // 邮件处理逻辑
    @Transactional
    public void processEmailData(Long userId, AiEmailData emailData) {
        try {
            aiEmailDataService.saveOrUpdate(emailData, new LambdaQueryWrapper<AiEmailData>()
                    .eq(AiEmailData::getMessageId, emailData.getMessageId()));

            if (CollectionUtils.isNotEmpty(emailData.getMaterialList())) {
                aiEmailAttachmentsService.remove(
                        new LambdaQueryWrapper<AiEmailAttachments>()
                                .eq(AiEmailAttachments::getEmailId, emailData.getId()));

                emailData.getMaterialList().forEach(item -> {
                    item.setEmailType("1");
                    item.setEmailId(emailData.getId());
                });
                aiEmailAttachmentsService.saveBatch(emailData.getMaterialList());

                cancelPendingTasks(userId, emailData);
            }
            log.info("用户----->{},收到邮件：{}", userId, emailData.getTheme());
        } catch (Exception e) {
            log.error("邮件处理失败：{}", e.getMessage(), e);
        }
    }

    // 取消待处理任务
    private void cancelPendingTasks(Long userId, AiEmailData emailData) {
        List<Long> ids = aiEmailService.list(new LambdaQueryWrapper<AiEmail>()
                .eq(AiEmail::getEmailAddress, emailData.getAddress())
                .eq(AiEmail::getRecipientEmailAddress, emailData.getFromEmail())
                .eq(AiEmail::getEmailType, 4)
                .eq(AiEmail::getEmailStatus, 0)
                .eq(AiEmail::getUserId, userId)
                .lt(AiEmail::getSendTime, emailData.getFromTime())
        ).stream().map(AiEmail::getId).collect(Collectors.toList());
        log.info("取消待处理任务：{}", ids);
        if (!ids.isEmpty()) {
            int tasks = emailScheduler.batchCancelTasks(ids);
            log.info("已取消待处理任务：{}", tasks);
        }
    }

    // 配置更新事件
    @Getter
    public static class ConfigUpdateEvent extends ApplicationEvent {
        private final Long userId;
        public ConfigUpdateEvent(Object source, Long userId) {
            super(source);
            this.userId = userId;
        }
    }

    // 初始化
    @PostConstruct
    public void init() {
        log.info("初始化邮箱监听服务...");
        executor = Executors.newCachedThreadPool();

        List<SysUser> sysUsers = sysUserService.list(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getDelFlag, '0'));

        if (CollectionUtils.isEmpty(sysUsers)) return;

        sysUsers.forEach(user -> {
            AiEmailConfig config = aiEmailConfigService.selectAiEmailConfigByUserId(user.getUserId());
            if (config != null) {
                addEmailConfig(user.getUserId());
            }
        });
    }

    // 动态添加配置
    public synchronized void addEmailConfig(Long userId) {
        removeEmailConfig(userId);  // 先移除旧配置

        AiEmailConfig config = aiEmailConfigService.selectAiEmailConfigByUserId(userId);
        if (config != null) {
            EmailConfig emailConfig = new EmailConfig(
                    userId,
                    config.getImapHost(),
                    config.getImapPort(),
                    config.getUsername(),
                    config.getPassword()
            );

            Future<?> future = executor.submit(new EmailListener(emailConfig, emailProcessor));
            taskMap.put(userId, future);
            log.info("动态添加邮箱监听: 用户------>{}", userId);
        }
    }

    // 动态移除配置
    public synchronized void removeEmailConfig(Long userId) {
        Future<?> future = taskMap.get(userId);
        if (future != null) {
            future.cancel(true);  // 中断监听线程
            taskMap.remove(userId);
            log.info("移除邮箱监听: 用户{}", userId);
        }
    }

    // 配置变更处理
    @EventListener
    public void handleConfigUpdate(ConfigUpdateEvent event) {
        addEmailConfig(event.getUserId());
    }

    // 服务关闭
    @PreDestroy
    public void shutdown() {
        log.info("关闭邮箱监听服务...");
        taskMap.keySet().forEach(this::removeEmailConfig);
        if (executor != null) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.warn("线程池未完全关闭，强制终止");
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("邮箱监听服务已停止");
    }

    @Override
    public void run() {}

    // 邮箱配置类
    static class EmailConfig {
        final Long userId;
        final String host;
        final String port;
        final String username;
        final String password;
        final String folderName = "INBOX";

        public EmailConfig(Long userId, String host, String port, String username, String password) {
            this.userId = userId;
            this.host = host;
            this.port = port;
            this.username = username;
            this.password = password;
        }
    }

    // 邮箱监听线程
    static class EmailListener implements Runnable {
        private final EmailConfig config;
        private final BiConsumer<Long, AiEmailData> processor;
        private volatile boolean running = true;
        private IMAPFolder currentFolder;
        private long lastUid = -1;
        private long lastHeartbeatTime = 0;
        private int reconnectAttempts = 0;

        public EmailListener(EmailConfig config, BiConsumer<Long, AiEmailData> processor) {
            this.config = config;
            this.processor = processor;
        }

        @Override
        public void run() {
            Thread currentThread = Thread.currentThread();
            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    currentFolder = connect();
                    watchFolder(currentFolder);
                } catch (FolderClosedException e) {
                    log.warn("【{}】文件夹意外关闭，10秒后重连", config.username);
                    closeResources();
                    sleep(10_000);
                } catch (Exception e) {
                    log.error("【{}】连接异常：{}，10秒后重试", config.username, e.getMessage());
                    closeResources();
                    sleep(10_000);
                }
            }
            closeResources();
        }

        // 连接邮箱服务器
        private IMAPFolder connect() throws Exception {
            Properties props = new Properties();
            props.put("mail.store.protocol", "imap");
            props.put("mail.imap.host", config.host);
            props.put("mail.imap.port", config.port);
            props.put("mail.imap.connectiontimeout", "30000");
            props.put("mail.imap.timeout", "60000");
            props.put("mail.imap.writetimeout", "40000");

            boolean is163Email = config.host.contains("163.com");
            if (is163Email) {
                props.put("mail.imap.auth.plain.disable", "true");
                props.put("mail.imap.auth.login.disable", "false");
                props.put("mail.imap.ssl.checkserveridentity", "false");
                props.put("mail.imap.ssl.trust", "*");
                props.put("mail.imap.connectionpoolsize", "1");
                try {
                    String localIP = InetAddress.getLocalHost().getHostAddress();
                    props.put("mail.imap.localaddress", localIP);
                } catch (UnknownHostException ex) {
                    log.warn("无法获取本地IP地址", ex);
                }
            }

            if ("993".equals(config.port)) {
                props.put("mail.imap.ssl.enable", "true");
                props.put("mail.imap.ssl.protocols", "TLSv1.2");
            } else if ("143".equals(config.port)) {
                props.put("mail.imap.starttls.enable", "true");
                props.put("mail.imap.starttls.required", "true");
            }

            IMAPStore store = null;
            try {
                Session session = Session.getInstance(props, null);
                store = (IMAPStore) session.getStore("imap");

                if (is163Email) {
                    store.connect(config.host, config.username, config.password);
                    Map<String, String> clientID = new HashMap<>();
                    clientID.put("name", "EnterpriseMailClient");
                    clientID.put("version", "1.0.0");
                    clientID.put("vendor", "YourCompany");
                    clientID.put("support-email", "<EMAIL>");
                    store.id(clientID);
                    sleep(300);
                } else {
                    store.connect(config.host, Integer.parseInt(config.port), config.username, config.password);
                }

                if (!store.isConnected()) {
                    throw new MessagingException("IMAP连接失败: 未建立有效连接");
                }

                IMAPFolder folder = (IMAPFolder) store.getFolder("INBOX");
                if (!folder.exists()) {
                    throw new FolderNotFoundException();
                }

                int openMode = (is163Email) ? Folder.READ_ONLY : Folder.READ_WRITE;
                folder.open(openMode);
                return folder;
            } catch (AuthenticationFailedException e) {
                String errorMsg = "邮件认证失败: ";
                if (is163Email) {
                    errorMsg += "\n可能原因: "
                            + "\n  1. 使用了登录密码而非授权码"
                            + "\n  2. IMAP服务未在网页邮箱启用"
                            + "\n  3. 账号未通过安全检查";
                }
                throw new RuntimeException(errorMsg, e);
            } catch (Exception e) {
                throw new RuntimeException("连接失败: " + e.getMessage(), e);
            }
        }

        // 监听文件夹
        private void watchFolder(IMAPFolder folder) {
            try {
                folder.addMessageCountListener(new MessageCountAdapter() {
                    @Override
                    public void messagesAdded(MessageCountEvent event) {
                        processNewMessages(event.getMessages());
                    }
                });

                boolean supportsIdle = folder.getStore() instanceof IMAPStore
                        && ((IMAPStore) folder.getStore()).hasCapability("IDLE");

                if (supportsIdle) {
                    while (running && folder.isOpen()) {
                        try {
                            folder.idle(true);
                        } catch (FolderClosedException e) {
                            log.warn("【{}】IDLE连接中断", config.username);
                            break;
                        } catch (MessagingException e) {
                            log.error("【{}】IDLE异常: {}", config.username, e.getMessage());
                            sleep(5000);
                        }
                    }
                } else {
                    pollForNewMessages(folder);
                }
            } catch (Exception e) {
                log.error("监听失败: {}", e.getMessage());
            }
        }

        // 轮询模式
        private void pollForNewMessages(IMAPFolder folder) {
            try {
                if (lastUid == -1 && folder.isOpen()) {
                    Message[] messages = folder.getMessages();
                    if (messages.length > 0) {
                        lastUid = folder.getUID(messages[messages.length - 1]);
                    }
                }
            } catch (MessagingException e) {
                log.warn("【{}】UID初始化失败: {}", config.username, e.getMessage());
            }

            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    if (System.currentTimeMillis() - lastHeartbeatTime > 120_000) {
                        sendHeartbeat(folder);
                        lastHeartbeatTime = System.currentTimeMillis();
                    }

                    if (!isConnectionAlive(folder)) {
                        folder = reconnectFolder(folder);
                        continue;
                    }

                    Message[] unreadMessages = folder.search(
                            new FlagTerm(new Flags(Flags.Flag.SEEN), false)
                    );

                    if (unreadMessages.length > 0) {
                        long currentMaxUid = folder.getUID(unreadMessages[unreadMessages.length - 1]);
                        if (currentMaxUid > lastUid) {
                            processNewMessages(unreadMessages);
                            lastUid = currentMaxUid;
                        }
                    }
                    sleep(30_000);
                } catch (MessagingException e) {
                    folder = handlePollingError(folder, e);
                    if (folder == null || !folder.isOpen()) {
                        break; // 退出循环避免空指针
                    }
                } catch (Exception e) {
                    log.error("【{}】轮询严重错误: {}", config.username, e.getMessage());
                    break;
                }
            }
        }

        // 发送心跳
        private void sendHeartbeat(IMAPFolder folder) {
            try {
                folder.doCommand(p -> {
                    p.simpleCommand("NOOP", null);
                    return null;
                });
            } catch (MessagingException e) {
                log.warn("【{}】心跳失败: {}", config.username, e.getMessage());
            }
        }

        // 连接重建
        private IMAPFolder reconnectFolder(IMAPFolder oldFolder) {
            closeFolderSafely(oldFolder);
            try {
                return connect();
            } catch (Exception e) {
                return handleReconnectFailure(e);
            }
        }

        // 指数退避重连
        private IMAPFolder handleReconnectFailure(Exception e) {
            if (reconnectAttempts++ >= 3) {
                log.error("【{}】连续重建失败，停止线程", config.username, e);
                running = false;
                return null;
            }
            long delay = 5000 * (long) Math.pow(2, reconnectAttempts);
            log.warn("【{}】等待{}ms后重试: {}", config.username, delay, e.getMessage());
            sleep(delay);
            return reconnectFolder(currentFolder);
        }

        // 连接状态检测
        private boolean isConnectionAlive(IMAPFolder folder) {
            try {
                return folder != null
                        && folder.isOpen()
                        && folder.getStore().isConnected();
            } catch (Exception e) {
                return false;
            }
        }

        // 异常处理
        private IMAPFolder handlePollingError(IMAPFolder folder, Exception e) {
            closeFolderSafely(folder);
            sleep(5_000);
            try {
                return reconnectFolder(folder);
            } catch (Exception ex) {
                running = false;
                return null;
            }
        }

        // 处理新邮件
        private void processNewMessages(Message[] messages) {
            for (Message message : messages) {
                try {
                    AiEmailData aiEmailData = MailUtil.parseSingleMessage(config.userId, (MimeMessage) message);
                    assert aiEmailData != null;
                    if (!aiEmailData.getTheme().contains("Undeliverable")){
                        processor.accept(config.userId, aiEmailData);
                    }
                } catch (Exception e) {
                    log.error("邮件处理错误：{}", e.getMessage());
                }
            }
        }

        // 资源关闭
        private void closeResources() {
            if (currentFolder != null) {
                try {
                    if (currentFolder.isOpen()) {
                        currentFolder.close(false);
                    }
                    Store store = currentFolder.getStore();
                    if (store != null && store.isConnected()) {
                        store.close();
                    }
                } catch (MessagingException e) {
                    log.warn("关闭资源时出错: {}", e.getMessage());
                } finally {
                    currentFolder = null;
                }
            }
        }

        private void closeFolderSafely(IMAPFolder folder) {
            if (folder != null) {
                try {
                    if (folder.isOpen()) folder.close(false);
                } catch (MessagingException e) {
                    log.warn("关闭文件夹失败: {}", e.getMessage());
                }
            }
        }

        private void sleep(long millis) {
            try {
                Thread.sleep(millis);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                running = false;
            }
        }
    }
}